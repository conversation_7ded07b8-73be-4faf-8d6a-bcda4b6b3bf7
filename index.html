<!DOCTYPE html>
<html lang="en">
  <head>
    <meta charset="UTF-8" />
    <link rel="icon" type="image/svg+xml" href="/vite.svg" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=5.0, user-scalable=yes" />
    <meta http-equiv="Cache-Control" content="no-cache, no-store, must-revalidate" />
    <meta http-equiv="Pragma" content="no-cache" />
    <meta http-equiv="Expires" content="0" />

    <!-- Preload critical CSS to ensure responsive styles load first -->
    <link rel="preload" href="/src/index.css" as="style" onload="this.onload=null;this.rel='stylesheet'">
    <noscript><link rel="stylesheet" href="/src/index.css"></noscript>
    <title>Horizon Carpentry & Handyman - Fairmount GA Carpenter & Handyman Services</title>
    <meta
      name="description"
      content="Professional carpentry and handyman services in Fairmount, La Fayette, and North Georgia. Deck construction, home repairs, bathroom remodeling, and quality craftsmanship. Licensed, insured, and 4.7-star rated."
    />

    <!-- SEO Meta Tags -->
    <meta name="keywords" content="Horizon Carpentry, Fairmount GA carpenter, handyman Fairmount Georgia, deck builder La Fayette GA, home repairs North Georgia, bathroom remodeling Fairmount, construction company Georgia" />
    <meta name="author" content="Horizon Carpentry & Handyman" />
    <meta name="robots" content="index, follow" />

    <!-- Local Business Schema -->
    <script type="application/ld+json">
    {
      "@context": "https://schema.org",
      "@type": "LocalBusiness",
      "name": "Horizon Carpentry & Handyman",
      "image": "https://storage.googleapis.com/msgsndr/BK5WOlszHMZB0udM7qC1/media/6855f48942e8ef583415f8e8.webp",
      "description": "Professional carpentry and handyman services in Fairmount, La Fayette, and North Georgia",
      "address": {
        "@type": "PostalAddress",
        "streetAddress": "166 Spring St",
        "addressLocality": "Fairmount",
        "addressRegion": "GA",
        "postalCode": "30139",
        "addressCountry": "US"
      },
      "telephone": "(*************",
      "email": "<EMAIL>",
      "url": "https://horizoncarpentry.com",
      "openingHours": "Mo-Fr 09:00-18:00",
      "priceRange": "$$",
      "aggregateRating": {
        "@type": "AggregateRating",
        "ratingValue": "4.7",
        "reviewCount": "12"
      },
      "serviceArea": {
        "@type": "GeoCircle",
        "geoMidpoint": {
          "@type": "GeoCoordinates",
          "latitude": "34.4526",
          "longitude": "-84.9041"
        },
        "geoRadius": "50000"
      },
      "hasOfferCatalog": {
        "@type": "OfferCatalog",
        "name": "Carpentry and Handyman Services",
        "itemListElement": [
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Service",
              "name": "Deck Construction"
            }
          },
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Service",
              "name": "Bathroom Remodeling"
            }
          },
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Service",
              "name": "Home Repairs"
            }
          },
          {
            "@type": "Offer",
            "itemOffered": {
              "@type": "Service",
              "name": "Handyman Services"
            }
          }
        ]
      }
    }
    </script>

    <!-- Chat Widget Script -->
    <script
      src="https://beta.leadconnectorhq.com/loader.js"
      data-resources-url="https://beta.leadconnectorhq.com/chat-widget/loader.js"
      data-widget-id="685ae39e7e2b2461766c5b7d"
    ></script>

    <!-- Critical CSS for immediate responsive behavior -->
    <style>
      /* Critical responsive styles loaded immediately */
      html {
        -webkit-text-size-adjust: 100%;
        -ms-text-size-adjust: 100%;
        overflow-x: hidden;
      }

      * {
        box-sizing: border-box;
        -webkit-box-sizing: border-box;
        -moz-box-sizing: border-box;
      }

      body {
        margin: 0;
        padding: 0;
        overflow-x: hidden;
        font-family: system-ui, -apple-system, sans-serif;
      }

      /* Ensure proper responsive container behavior */
      .responsive-container {
        width: 100%;
        max-width: 100vw;
        margin: 0 auto;
      }

      /* Force proper responsive breakpoints */
      @media (min-width: 768px) {
        .responsive-container {
          max-width: 1280px;
          padding-left: 2rem;
          padding-right: 2rem;
        }
      }

      @media (min-width: 1024px) {
        .responsive-container {
          padding-left: 3rem;
          padding-right: 3rem;
        }
      }

      /* Global styles that may help with iframe content */
      iframe[src*="leadconnectorhq.com"] * {
        box-sizing: border-box !important;
      }

      /* Prevent FOUC */
      html:not(.loaded) {
        visibility: hidden;
        opacity: 0;
      }

      html.loaded {
        visibility: visible;
        opacity: 1;
        transition: opacity 0.15s ease-in-out;
      }
    </style>
  </head>
  <body>
    <!-- Responsive Detection Script - Runs immediately -->
    <script>
      (function() {
        // Immediate responsive detection and viewport fix
        function setResponsiveClasses() {
          const width = window.innerWidth || document.documentElement.clientWidth;
          const html = document.documentElement;

          // Remove all existing responsive classes
          html.classList.remove('mobile', 'tablet', 'desktop', 'large-desktop');

          // Add appropriate class based on actual viewport width
          if (width < 768) {
            html.classList.add('mobile');
            html.setAttribute('data-breakpoint', 'mobile');
          } else if (width < 1024) {
            html.classList.add('tablet');
            html.setAttribute('data-breakpoint', 'tablet');
          } else if (width < 1280) {
            html.classList.add('desktop');
            html.setAttribute('data-breakpoint', 'desktop');
          } else {
            html.classList.add('large-desktop');
            html.setAttribute('data-breakpoint', 'large-desktop');
          }

          // Force viewport meta tag update
          const viewport = document.querySelector('meta[name="viewport"]');
          if (viewport) {
            viewport.setAttribute('content', 'width=device-width, initial-scale=1.0, minimum-scale=1.0, maximum-scale=5.0, user-scalable=yes');
          }

          // Debug logging
          console.log('Responsive Detection:', {
            width: width,
            breakpoint: html.getAttribute('data-breakpoint'),
            userAgent: navigator.userAgent.substring(0, 50)
          });
        }

        // Run immediately
        setResponsiveClasses();

        // Run on resize with debouncing
        let resizeTimeout;
        window.addEventListener('resize', function() {
          clearTimeout(resizeTimeout);
          resizeTimeout = setTimeout(setResponsiveClasses, 100);
        });

        // Run on orientation change
        window.addEventListener('orientationchange', function() {
          setTimeout(setResponsiveClasses, 200);
        });

        // Ensure it runs after DOM is loaded
        if (document.readyState === 'loading') {
          document.addEventListener('DOMContentLoaded', setResponsiveClasses);
        }
      })();
    </script>

    <div id="root"></div>
    <script type="module" src="/src/main.tsx"></script>

    <!-- Form Embed Script -->
    <script src="https://link.msgsndr.com/js/form_embed.js" async></script>

    <!-- Vapi Voice AI Script -->
    <script
      src="https://unpkg.com/@vapi-ai/client-sdk-react/dist/embed/widget.umd.js"
      async
      type="text/javascript"
    ></script>

    <!-- Custom script to handle form styling -->
    <script>
      // Function to inject custom CSS into HighLevel iframes
      function injectHighLevelStyles() {
        const iframes = document.querySelectorAll(
          'iframe[src*="leadconnectorhq.com"]'
        );

        iframes.forEach((iframe) => {
          iframe.addEventListener("load", function () {
            try {
              // Try to access iframe content (may be blocked by CORS)
              const iframeDoc =
                iframe.contentDocument || iframe.contentWindow.document;

              if (iframeDoc) {
                const customStyles = `
                  <style>
                    /* Reset spacing on HighLevel's wrapper DIVs */
                    .field-container,
                    .field-container > div,
                    .col-12 {
                      padding: 0 !important;
                      margin: 2px 0 !important;
                    }

                    /* Hide unwanted, auto-generated address fields */
                    .non-address-elements,
                    #address,
                    #form-address {
                      display: none !important;
                    }

                    /* Hide default field labels if you use placeholders instead */
                    .form-builder--item label {
                      display: none !important;
                    }

                    .p {
                      font-size: 7px !important;
                    }

                    .extra-top-padding {
                      margin-top: 0 !important;
                    }

                    .fields-container {
                      padding-top: 0 !important;
                      padding-bottom: 0 !important;
                      margin-top: 0 !important;
                      margin-bottom: 0 !important;
                    }
                  </style>
                `;

                iframeDoc.head.insertAdjacentHTML("beforeend", customStyles);
              }
            } catch (e) {
              // CORS restriction - styles will need to be applied in HighLevel directly
              console.log("Cannot inject styles due to CORS policy");
            }
          });
        });
      }

      // Run when DOM is loaded
      document.addEventListener("DOMContentLoaded", function () {
        // Initial injection
        setTimeout(injectHighLevelStyles, 1000);

        // Re-inject periodically in case forms load dynamically
        setInterval(injectHighLevelStyles, 3000);
      });
    </script>
  </body>
</html>
